package llm;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import common.utils.NetworkUtils;
import common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import sdk.entity.CanDevice;
import sdk.entity.LightTestBoxDevice;
import ui.layout.aichat.UserPromptPanel;
import ui.layout.left.display.components.container.can.DbcFileManager;
import ui.layout.left.display.components.container.can.DbcFileObserver;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.SmartSequenceConvertDialog;
import ui.model.ExcelUploadRequest;
import ui.model.MainModel;
import ui.model.device.LightConfigLoadObserver;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

//TODO:迁移到后端
@Slf4j
public class ActionSequenceLlmScriptGenerator extends LLMProvider implements DbcFileObserver, LightConfigLoadObserver {
    public static final String ACTION_SEQUENCE_TASK = "action_sequence";
    private static ActionSequenceLlmScriptGenerator INSTANCE;

    private String sessionId;
    private final MainModel mainModel;
    private boolean needUpdateContextFiles;
    private HeartbeatManager heartbeatManager;

    public ActionSequenceLlmScriptGenerator(MainModel mainModel) {
        this.mainModel = mainModel;
        this.sessionId = null;
        needUpdateContextFiles = true;
        DbcFileManager.getInstance().addAllObserver(this);
        mainModel.getTestBoxModel().registerObserver(this);
//        System.out.println("LLM_BASE_URL:" + LLM_BASE_URL);
    }

    private void updateSessionId(String newSessionId) {
        if (heartbeatManager != null) {
            heartbeatManager.stop();
        }
        this.sessionId = newSessionId;
        // 创建并启动新的心跳
        this.heartbeatManager = new HeartbeatManager(newSessionId);
        this.heartbeatManager.start();
    }

    public static ActionSequenceLlmScriptGenerator getInstance(MainModel mainModel) {
        if (INSTANCE == null) {
            INSTANCE = new ActionSequenceLlmScriptGenerator(mainModel);
        }
        return INSTANCE;
    }

    public void reset() {
        log.info("重置dbc文件和仪器设备配置记忆");
        needUpdateContextFiles = true;
    }

    /**
     * 清理无效的dbc文件
     */
    public void cleanInvalidDbcFiles(List<DbcFile> dbcFiles) {
        List<DbcFile> files = new ArrayList<>(dbcFiles);
        for (DbcFile dbcFile : files) {
            File file = new File(dbcFile.getFilePath());
            if (!file.exists()) {
                log.warn("dbc文件未找到: {}", dbcFile.getFilePath());
                dbcFiles.remove(dbcFile);
            }
        }
    }

    private void clearResources() {
        log.info("清理LLM资源");
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(LLMProvider.LLM_CLEAR_RESOURCE + DeviceIdentifier.getDeviceId())
                .delete()
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                log.info("资源清理成功");
            } else {
                log.error("资源清理失败: {} - {}", response.code(), response.message());
            }
        } catch (IOException e) {
            log.error("资源清理过程中发生错误", e);
        }
    }

    private void uploadExcelFile(File file, String fileType) throws IOException {
        try {
            log.info("开始上传配置文件: {}", file.getAbsolutePath());
            ExcelUploadRequest excelUploadRequest = new ExcelUploadRequest();
            excelUploadRequest.setProject((mainModel.getAppInfo().getProject()));

            okhttp3.RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("request", new ObjectMapper().writeValueAsString(excelUploadRequest))
                    .addFormDataPart("file", file.getName(),
                            RequestBody.create(file, MediaType.get("application/vnd.ms-excel")))
                    .build();

            Request request = new Request.Builder()
                    .url(LLMProvider.LLM_UPLOAD_CONFIG + fileType)
                    .header("X-Session-ID", sessionId)  // 修改为后端配置的请求头
                    .post(requestBody)
                    .build();

            try (Response response = new OkHttpClient().newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    log.info("上传配置文件成功:{}", responseBody);
                } else {
                    String error = String.format("上传配置文件失败: %d - %s", response.code(), response.message());
                    log.error(error);
                    throw new IOException(error);
                }
            }
        } catch (Exception e) {
            throw new IOException(e);

        }
    }

    public void uploadTestBoxConfig() throws IOException {
        String file = LightTestBoxDevice.getConfigFile();
        if (!StringUtils.isEmpty(file)) {
            uploadExcelFile(new File(file), "bic");
        }
    }

    public void uploadHudConfig() throws IOException {
        String file = SmartSequenceConvertDialog.getInstance(mainModel).getHudConfigFile();
        if (!StringUtils.isEmpty(file)) {
            uploadExcelFile(new File(file), "hud");
        }
    }

    /**
     * @param dbcFiles dbc文件列表
     */
    public void uploadDbcFiles(List<DbcFile> dbcFiles) throws IOException {
        String project = mainModel.getAppInfo().getProject();
        log.info("开始上传dbc文件 - 项目名: {}, DBC文件数量: {}",
                project, dbcFiles != null ? dbcFiles.size() : 0);

        // 验证输入参数
        if (dbcFiles == null) {
            log.error("dbc文件列表为空");
            throw new IOException("dbc文件不能为空");
        }
        //清理无效dbc
        log.info("上传dbc文件：{}", dbcFiles);
        cleanInvalidDbcFiles(dbcFiles);
        if (dbcFiles.isEmpty()) {
            return;
        }
        URL url = new URL(LLM_DBC_UPLOAD_URL);
        log.info("正在连接服务器: {}", LLM_DBC_UPLOAD_URL);

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        // 设置超时
        connection.setConnectTimeout(LONG_TIMEOUT);
        connection.setReadTimeout(LONG_TIMEOUT);
        connection.setRequestMethod("POST");
        connection.setDoOutput(true);

        // 设置会话ID
        if (sessionId != null) {
            log.info("使用会话ID: {}", sessionId);
            connection.setRequestProperty("X-Session-ID", sessionId);
        }

        // 设置multipart表单边界
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
        connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

        // 使用 DataOutputStream 替代 PrintWriter
        try (OutputStream os = connection.getOutputStream();
             DataOutputStream writer = new DataOutputStream(os)) {

            // 准备上传请求对象
            DbcUploadRequest dbcUploadRequest = new DbcUploadRequest();
            dbcUploadRequest.setProject(project);
            dbcUploadRequest.setDbcFiles(dbcFiles);

            String requestJson = JSON.toJSONString(dbcUploadRequest);
            log.info("准备上传请求数据: {}", requestJson);

            // 写入请求部分
            String requestPart = "--" + boundary + "\r\n" +
                    "Content-Disposition: form-data; name=\"request\"\r\n\r\n" +
                    requestJson + "\r\n";
            writer.write(requestPart.getBytes(StandardCharsets.UTF_8));

            // 添加文件部分
            for (DbcFile dbcFile : dbcFiles) {
                File file = new File(dbcFile.getFilePath());
                log.info("正在处理文件: {}, 文件大小: {} 字节", file.getName(), file.length());

                // 写入文件头部
                String fileHeader = "--" + boundary + "\r\n" +
                        "Content-Disposition: form-data; name=\"files\"; filename=\"" + file.getName() + "\"\r\n" +
                        "Content-Type: application/octet-stream\r\n\r\n";
                writer.write(fileHeader.getBytes(StandardCharsets.UTF_8));

                // 写入文件内容
                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    int totalBytes = 0;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        writer.write(buffer, 0, bytesRead);
                        totalBytes += bytesRead;
                    }
                    log.info("已上传 {} 字节, 文件名: {}", totalBytes, file.getName());
                }
                writer.write("\r\n".getBytes(StandardCharsets.UTF_8));
            }

            // 写入结束分隔符
            writer.write(("--" + boundary + "--\r\n").getBytes(StandardCharsets.UTF_8));
            writer.flush();

            StringBuilder response = new StringBuilder();
            int responseCode = connection.getResponseCode();
            log.info("服务器响应状态码: {}", responseCode);

            // 处理响应数据
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 400 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            } finally {
                log.info("上传dbc完成");
            }

            String responseStr = response.toString();
            log.info("服务器响应内容: {}", responseStr);

            // 处理响应结果
            if (responseCode != 200) {
                log.error("服务器返回错误状态码: {} 响应内容: {}", responseCode, responseStr);
                throw new IOException("上传dbc错误: " + responseCode + " - " + responseStr);
            }
        } catch (SocketTimeoutException e) {
            log.error("dbc文件上传过程中发生错误", e);
            throw new IOException("dbc上传失败: " + e.getMessage());
        }
    }

    private boolean checkSessionResourceExists(String resourceId) throws IOException {
        if (sessionId == null || resourceId == null) {
            return false;
        }

        log.info("检查资源ID是否存在资源: {}", resourceId);
        URL url = new URL(LLM_CHECK_SESSION_RESOURCE_IF_EXIST + "/" + resourceId);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setReadTimeout(LONG_TIMEOUT);
        connection.setConnectTimeout(LONG_TIMEOUT);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            log.error("检查资源状态失败: {}", responseCode);
            return false;
        }

        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }

            JSONObject jsonResponse = JSON.parseObject(response.toString());
            boolean exists = jsonResponse.getBooleanValue("exists");
            log.info("会话状态检查结果 - 资源ID: {}, 是否存在: {}", resourceId, exists);
            return exists;
        }
    }

    private void uploadFiles() throws IOException {
        List<DbcFile> dbcFiles = CanDevice.getDbcFileList();
        uploadDbcFiles(dbcFiles);
        uploadTestBoxConfig();
        uploadHudConfig();
        needUpdateContextFiles = false;
    }

    private void ensureResourceUploaded() throws IOException {
        if (!checkSessionResourceExists(DeviceIdentifier.getDeviceId()) || needUpdateContextFiles) {
            log.info("会话不存在或配置已失效，重新上传DBC和配置文件");
            clearResources();
            uploadFiles();
        }
    }

    /**
     * 生成动作序列组合
     *
     * @param actionSequenceRequest 请求体
     * @return LLM响应体
     */
    public LLMResponse generateCombinedActionSequences(ActionSequenceRequest actionSequenceRequest) throws IOException {
        log.info("启动动作序列组合生成: {}", actionSequenceRequest);
        ensureResourceUploaded();

        URL url = new URL(LLM_ACTION_SEQUENCE_GENERATION_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(LONG_TIMEOUT);
        connection.setReadTimeout(LONG_TIMEOUT);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        if (sessionId != null) {
            connection.setRequestProperty("X-Session-ID", sessionId);
        }
        log.info("LLM会话ID: {}", sessionId);
        // 写入请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] inputBytes = JSON.toJSONBytes(actionSequenceRequest);
            os.write(inputBytes, 0, inputBytes.length);
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        int responseCode = connection.getResponseCode();

        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8
                )
        )) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        // 解析响应
        String responseStr = response.toString();
        if (responseCode == 200) {
            JSONObject jsonResponse = JSON.parseObject(responseStr);
            LLMResponse llmResponse = new LLMResponse();

            // 解析返回的ActionSequenceRequest结构
            JSONObject resultObj = jsonResponse.getJSONObject("result");
            ActionSequenceRequest responseRequest = new ActionSequenceRequest();
            responseRequest.setCondition(resultObj.getString("condition"));
            responseRequest.setAction(resultObj.getString("action"));
            responseRequest.setExpect(resultObj.getString("expect").replaceAll("\n", "\\\\n"));

            // 将ActionSequenceRequest转换为JSON字符串作为result
            llmResponse.setResult(JSON.toJSONString(responseRequest));
            llmResponse.setSessionId(jsonResponse.getString("session_id"));
            llmResponse.setActionSequenceRequest(responseRequest);

            log.info("动作序列生成返回: {}", llmResponse);
            return llmResponse;
        } else {
            // 尝试解析错误响应为JSON
            try {
                JSONObject errorResponse = JSON.parseObject(responseStr);
                String errorDetail = errorResponse.getString("detail");
                throw new LLMException(responseCode, errorDetail != null ? errorDetail : responseStr);
            } catch (JSONException e) {
                // 如果无法解析为JSON，则直接抛出原始错误信息
                throw new LLMException(responseCode, responseStr);
            }
        }
    }

    /**
     * 生成单条动作序列
     *
     * @param input 用户输入
     * @return LLMResponse
     */
    public LLMResponse generateSingleActionSequence(String input) throws IOException {
        log.info("启动单条动作序列生成");
        ensureResourceUploaded();

        URL url = new URL(LLM_GENERATION_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setReadTimeout(LONG_TIMEOUT);
        connection.setConnectTimeout(LONG_TIMEOUT);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        if (sessionId != null) {
            connection.setRequestProperty("X-Session-ID", sessionId);
        }

        JSONObject requestBody = new JSONObject();
        requestBody.put("input", input);
        requestBody.put("department", mainModel.getAppInfo().getBuName());
        requestBody.put("task_type", ACTION_SEQUENCE_TASK);
        requestBody.put("user_prompt", UserPromptPanel.getInstance(mainModel).getPrompt());
        requestBody.put("system_prompt", SystemPrompt.getInstance().getPrompt());

        log.info("动作序列请求体:{}", requestBody);

        // 写入请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] inputBytes = JSON.toJSONBytes(requestBody);
            os.write(inputBytes, 0, inputBytes.length);
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        int responseCode = connection.getResponseCode();

        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8
                )
        )) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        // 解析响应
        String responseStr = response.toString();
        if (responseCode == 200) {
            JSONObject jsonResponse = JSON.parseObject(responseStr);
            LLMResponse llmResponse = new LLMResponse();
            llmResponse.setResult(jsonResponse.getString("result"));
            llmResponse.setSessionId(jsonResponse.getString("session_id"));
            log.info("大模型返回:{}", llmResponse);
            return llmResponse;
        } else {
            // 尝试解析错误响应为JSON
            try {
                JSONObject errorResponse = JSON.parseObject(responseStr);
                String errorDetail = errorResponse.getString("detail");
                throw new LLMException(responseCode, errorDetail != null ? errorDetail : responseStr);
            } catch (JSONException e) {
                // 如果无法解析为JSON，则直接抛出原始错误信息
                throw new LLMException(responseCode, responseStr);
            }
        }
    }

    /**
     * 开始生成动作序列
     *
     * @param totalCases 总案例数量
     * @return 开始生成动作序列的响应
     */
    public StartGenerationResponse startGeneration(int totalCases) throws IOException {
        log.info("开始生成：{}，案例总数量:{}", LLM_START_GENERATION_URL, totalCases);
        URL url = new URL(LLM_START_GENERATION_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setReadTimeout(LONG_TIMEOUT);
        connection.setConnectTimeout(LONG_TIMEOUT);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 使用SessionRequest类构建请求体
        SessionRequest sessionRequest = new SessionRequest();
        sessionRequest.setResourceId(DeviceIdentifier.getDeviceId());
        sessionRequest.setUser(mainModel.getAppInfo().getUserName());
        sessionRequest.setDepartment(mainModel.getAppInfo().getBuName());
        sessionRequest.setProject(mainModel.getAppInfo().getProject());
        sessionRequest.setComputer(NetworkUtils.getComputerInfo().getComputerName());
        sessionRequest.setTotalCases(String.valueOf(totalCases));

        // 将SessionRequest对象转换为JSON字符串
        String requestBody = JSON.toJSONString(sessionRequest);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] inputBytes = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(inputBytes, 0, inputBytes.length);
        }

        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8
                )
        )) {
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line.trim());
            }
        }

        String responseStr = response.toString();
        if (responseCode == 200) {
            StartGenerationResponse startResponse = JSON.parseObject(responseStr, StartGenerationResponse.class);
            log.info("生成已启动: {}", startResponse);
            String sessionIdFromServer = startResponse.getSessionId();
            if (sessionIdFromServer != null) {
                log.info("成功获取新的会话ID: {}", sessionIdFromServer);
                // 开始心跳
                updateSessionId(sessionIdFromServer);
            } else {
                throw new IOException("启动生成失败，获取的会话ID为空");
            }
            return startResponse;
        } else {
            log.error("启动生成失败: {} - {}", responseCode, responseStr);
            throw new IOException("启动生成失败: " + responseCode + " - " + responseStr);
        }
    }

    public void interruptGenerateScript() throws IOException {
        log.info("中断生成，LLM会话ID: {}", sessionId);

        if (sessionId == null) {
            throw new IOException("LLM会话ID不存在");
        }

        // 先停止心跳，避免并发问题
        if (heartbeatManager != null) {
            heartbeatManager.stop();
            heartbeatManager = null;
        }

        try {
            URL url = new URL(String.format(LLM_INTERRUPT_GENERATION_URL, sessionId));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(SHORT_TIMEOUT);  // 添加超时设置
            connection.setReadTimeout(SHORT_TIMEOUT);     // 添加超时设置
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                log.error("中断生成失败，状态码: {}", responseCode);
                throw new IOException("中断生成失败: " + responseCode);
            }

            log.info("已中断生成，LLM会话ID: {}", sessionId);
        } catch (Exception e) {
            log.error("中断生成时发生错误: {}", e.getMessage());
            throw new IOException("中断生成失败: " + e.getMessage());
        }
    }


    public void stopGenerateScript() throws IOException {
        log.info("停止生成，LLM会话ID: {}", sessionId);

        if (sessionId == null) {
            throw new IOException("LLM会话ID不存在");
        }

        // 先停止心跳，避免并发问题
        if (heartbeatManager != null) {
            heartbeatManager.stop();
            heartbeatManager = null;
        }

        try {
            URL url = new URL(String.format(LLM_STOP_GENERATION_URL, sessionId));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(SHORT_TIMEOUT);  // 添加超时设置
            connection.setReadTimeout(SHORT_TIMEOUT);     // 添加超时设置
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                log.error("停止生成失败，状态码: {}", responseCode);
                throw new IOException("停止生成失败: " + responseCode);
            }

            log.info("已停止生成，LLM会话ID: {}", sessionId);
        } catch (Exception e) {
            log.error("停止生成时发生错误: {}", e.getMessage());
            throw new IOException("停止生成失败: " + e.getMessage());
        }
    }

    public ProgressResponse updateProgress(int currentCase) throws IOException {
        log.info("更新进度，LLM会话ID: {}, 当前用例: {}", sessionId, currentCase);

        if (sessionId == null) {
            throw new IOException("LLM会话ID不存在");
        }
        URL url = new URL(String.format(LLM_UPDATE_PROGRESS_URL, sessionId));
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(SHORT_TIMEOUT);
        connection.setReadTimeout(SHORT_TIMEOUT);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 写入请求体
        JSONObject body = new JSONObject();
        body.put("current_case", currentCase);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] inputBytes = body.toJSONString().getBytes(StandardCharsets.UTF_8);
            os.write(inputBytes, 0, inputBytes.length);
        }

        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8
                )
        )) {
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line.trim());
            }
        }

        String responseStr = response.toString();
        if (responseCode == 200) {
            ProgressResponse progressResponse = JSON.parseObject(responseStr, ProgressResponse.class);
            log.info("进度已更新: {}", progressResponse);
            return progressResponse;
        } else {
            log.error("更新进度失败: {} - {}", responseCode, responseStr);
            throw new IOException("更新进度失败: " + responseCode + " - " + responseStr);
        }
    }

    @Override
    public void onDbcFileChanged(List<String> dbcFilePath) {
        reset();
    }

    @Override
    public void onConfigLoadSuccess(String configPath) {
        reset();
    }

    @Override
    public void onConfigLoadFailed(String errorMessage) {
    }

    @Override
    protected void finalize() throws Throwable {
        if (heartbeatManager != null) {
            heartbeatManager.stop();
        }
        super.finalize();
    }

}