package ui.model.testcase;

import ui.base.BaseModel;
import ui.callback.InsertTestCaseListener;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.InitScriptModel;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.TemplateRow;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.context.CaseContext;
import ui.layout.right.components.testcase.TestStep;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

public class TestCaseTableModel implements BaseModel, ModelObservable, TestCaseTableEventObserver{
    private final List<ModelObserver> modelObservers = new CopyOnWriteArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void startTest(CaseContext caseContext) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).startTest(caseContext);
        }
    }

    @Override
    public void outputStatusInfo(String info) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).outputStatusInfo(info);
        }
    }

    @Override
    public void outputRowExecuteIntervalTimeInfo(int time) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).outputRowExecuteIntervalTimeInfo(time);
        }
    }

    @Override
    public void outputFilterTableHeaderInfo(boolean isFilter) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).outputFilterTableHeaderInfo(isFilter);
        }
    }


    @Override
    public void outputTableRowCountInfo(int rowCount) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).outputTableRowCountInfo(rowCount);
        }
    }


    @Override
    public void pauseTest() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).pauseTest();
        }
    }

    @Override
    public void resumeTest() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).resumeTest();
        }
    }

    @Override
    public void stopTest() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).stopTest();
        }
    }

    @Override
    public void pauseTestTime() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).pauseTestTime();
        }
    }

    @Override
    public void resumeTestTime() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).resumeTestTime();
        }
    }

    @Override
    public void completedTest() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).completedTest();
        }
    }

    @Override
    public void testCaseLoaded() {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).testCaseLoaded();
        }
    }

    @Override
    public synchronized void updateExcelCaseTable(int row, List<TestStep> testStepList) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).updateExcelCaseTable(row, testStepList);
        }
    }

    @Override
    public synchronized void syncButtonStatus(Map<String, Boolean> map) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).syncButtonStatus(map);
        }
    }

    @Override
    public void upgradeOperation(String msg) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).upgradeOperation(msg);
        }
    }


    @Override
    public void freshTestStepData(int row) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).freshTestStepData(row);
        }
    }

    public void showTemplateInfo(List<TemplateRow> selectedRowTemplateList, List<TemplateRow> selectedRowFailPicList) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).showTemplateInfo(selectedRowTemplateList, selectedRowFailPicList);
        }
    }

    public void toCameraPage(boolean isToCamera, String tabName) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).toCameraPage(isToCamera, tabName);
        }
    }

    public void updateTemplateTable(boolean isUpdate) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).updateTemplateTable(isUpdate);
        }
    }

    public void saveExcelCaseTable(boolean isSave) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).saveExcelCaseTable(isSave);
        }
    }

    public void update(Boolean update) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).update(update);
        }
    }

    public void updateDIDToExcelCaseTable(String selectedProtocol, Map<Integer, Map<String, String>> didMap)  {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).updateDIDToExcelCaseTable(selectedProtocol, didMap);
        }
    }

    @Override
    public void insertInitScripts(InitScriptModel initScriptModel, InsertTestCaseListener listener) {
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).insertInitScripts(initScriptModel, listener);
        }
    }

    @Override
    public  void onUpgradeSuccess(boolean success){
        for (ModelObserver observer : modelObservers) {
            ((TestCaseTableEventObserver) observer).onUpgradeSuccess(success);
        }
    }
}
