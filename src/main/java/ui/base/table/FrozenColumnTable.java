package ui.base.table;

import excelcase.config.json.CaseConfigJsonManager;
import excelcase.config.json.CaseContent;
import excelcase.config.json.CaseRowContent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.base.renderer.FrozenCheckBoxRenderer;
import ui.base.renderer.RowHeaderRenderer;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.MatteBorder;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class FrozenColumnTable {
    private final JTable mainTable;
    private ExcelCaseTable excelTable;
    private final JTable fixedTable;
    @Getter
    private final JTable rowHeaderTable;
    private final JScrollPane scrollPane;
    private int fixedColumns;
    private final List<TableColumn> allColumns;
    private final JTableHeader head;
    private final JCheckBox selectCheckBox;
    private boolean enableSelectBoxChanged;
    private final MainModel mainModel;
    private TableRowSorter<TableModel> rowHeaderSorter;
    private RowFilter<Object, Object> filter;
    private volatile boolean isSyncing = false;

    /**
     * 获取同步状态
     */
    public boolean isSyncing() {
        return isSyncing;
    }

    /**
     * 设置同步状态
     */
    public void setSyncing(boolean syncing) {
        this.isSyncing = syncing;
    }

    public FrozenColumnTable(int fixedColumns, JScrollPane scrollPane) {
        this(null, fixedColumns, scrollPane);
    }

    public FrozenColumnTable(MainModel model, int fixedColumns, JScrollPane scrollPane) {
        this.mainModel = model;
        this.scrollPane = scrollPane;
        mainTable = (JTable) scrollPane.getViewport().getView();
        if (mainTable instanceof ExcelCaseTable) {
            excelTable = (ExcelCaseTable) mainTable;
        }

        // 创建用于冻结列的表格
        fixedTable = new JTable();
        fixedTable.setAutoCreateColumnsFromModel(false);
        fixedTable.setModel(mainTable.getModel());
        fixedTable.setSelectionModel(mainTable.getSelectionModel());
        fixedTable.setFocusable(false);

        //创建行号和复选表格
        rowHeaderTable = new JTable(new AbstractTableModel() {
            @Override
            public int getRowCount() {
                // 使用主表的实际可见行数，考虑过滤器的影响
                if (mainTable.getRowSorter() != null) {
                    return mainTable.getRowCount();
                } else {
                    return mainTable.getModel().getRowCount();
                }
            }

            @Override
            public int getColumnCount() {
                return 2;
            }

            @Override
            public Object getValueAt(int rowIndex, int columnIndex) {
                if (columnIndex == 0) {
                    // 显示过滤后的实际行号
                    if (mainTable.getRowSorter() != null) {
                        int modelRow = mainTable.convertRowIndexToModel(rowIndex);
                        return modelRow + 1; // 行号从 1 开始，显示原始行号
                    } else {
                        return rowIndex + 1;
                    }
                } else {
                    // 获取过滤后对应的模型行数据
                    if (mainTable.getRowSorter() != null) {
                        int modelRow = mainTable.convertRowIndexToModel(rowIndex);
                        return mainTable.getModel().getValueAt(modelRow, 0);
                    } else {
                        return mainTable.getModel().getValueAt(rowIndex, 0);
                    }
                }
            }

            @Override
            public String getColumnName(int column) {
                if (column == 0) {
                    return "";
                } else {
                    return "";
                }
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 1) {
                    return Boolean.class; // 固定列是复选框
                }
                return super.getColumnClass(columnIndex);
            }

            @Override
            public void setValueAt(Object value, int rowIndex, int columnIndex) {
                if (columnIndex == 1) {
                    // 设置过滤后对应的模型行数据
                    if (mainTable.getRowSorter() != null) {
                        int modelRow = mainTable.convertRowIndexToModel(rowIndex);
                        mainTable.getModel().setValueAt(value, modelRow, 0);
                    } else {
                        mainTable.getModel().setValueAt(value, rowIndex, 0);
                    }
                    fireTableCellUpdated(rowIndex, columnIndex);
                }
            }
        });

        // 不使用独立的 RowSorter，而是直接同步主表的过滤状态
        // rowHeaderTable 不需要独立的 RowSorter，因为它的数据直接从 mainTable 获取
        rowHeaderSorter = null;

        //禁止拖动列
        head = rowHeaderTable.getTableHeader();
        head.setResizingAllowed(false);
        head.setReorderingAllowed(false);

        JTableHeader mainHead = mainTable.getTableHeader();
        selectCheckBox = new JCheckBox("全选");

        // 设置行号表格的渲染器
        rowHeaderTable.setPreferredScrollableViewportSize(new Dimension(100, 0));

        rowHeaderTable.getColumnModel().getColumn(0).setCellRenderer(new RowHeaderRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                if (c instanceof JComponent) {
                    ((JComponent) c).setBorder(new MatteBorder(1, 1, 1, 0, table.getGridColor()));
                }
                return c;
            }
        });

        FrozenCheckBoxRenderer checkBoxCellRenderer = new FrozenCheckBoxRenderer();
        rowHeaderTable.getColumnModel().getColumn(1).setCellRenderer(checkBoxCellRenderer);

        head.setDefaultRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                if (column == -2) {
                    //行号
                    return super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                }
                String valueStr = String.valueOf(value);
                JLabel label = new JLabel(valueStr);
                label.setHorizontalAlignment(SwingConstants.CENTER);
                selectCheckBox.setHorizontalAlignment(SwingConstants.CENTER);// 表头标签居中
                selectCheckBox.setBorderPainted(true);
                JComponent component = column == 1 ? selectCheckBox : label;
                component.setFont(mainHead.getFont());
                return component;
            }
        });
        createActions();
        afterDataLoaded();

        rowHeaderTable.setRowHeight(mainTable.getRowHeight());
        rowHeaderTable.setFocusable(false);
        rowHeaderTable.setSelectionModel(mainTable.getSelectionModel());
        rowHeaderTable.setComponentPopupMenu(mainTable.getComponentPopupMenu());
        rowHeaderTable.setShowGrid(true);
        rowHeaderTable.setGridColor(mainTable.getGridColor());
        //隐藏主表的选择列
        mainTable.getColumnModel().getColumn(0).setMinWidth(0);
        mainTable.getColumnModel().getColumn(0).setMaxWidth(0);
        mainTable.getColumnModel().getColumn(0).setPreferredWidth(0);

        this.fixedColumns = fixedColumns;

        // 保存所有列
        allColumns = new ArrayList<>();
        for (int i = 0; i < mainTable.getColumnModel().getColumnCount(); i++) {
            allColumns.add(mainTable.getColumnModel().getColumn(i));
        }

        // 同步表格属性
        syncTableProperties();

        updateFrozenColumns(fixedColumns);

        // 确保行号表格随着主表格的垂直滚动条滚动
        syncVerticalScrolling();
    }

    public void createActions() {
        selectCheckBox.addItemListener(e -> {
            if (enableSelectBoxChanged) {
                excelTable.selectAllEvent(selectCheckBox.isSelected());
            }
        });
        head.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() > 0) {
                    //获得选中列
                    int selectColumn = head.columnAtPoint(e.getPoint());
                    if (selectColumn == 1) {
                        boolean value = !selectCheckBox.isSelected();
                        selectCheckBox.setSelected(value);
                        selectAllOrNull(value);
                        head.repaint();
                    }
                }
            }
        });
        // 添加鼠标监听器
        rowHeaderTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = rowHeaderTable.rowAtPoint(e.getPoint());
                int col = rowHeaderTable.columnAtPoint(e.getPoint());
                if (col == 1 && SwingUtilities.isLeftMouseButton(e)) { // 点击的是复选框列
                    boolean currentValue = (boolean) rowHeaderTable.getModel().getValueAt(row, col);
                    rowHeaderTable.getModel().setValueAt(!currentValue, row, col);

                    // 获取对应的模型行索引
                    int modelRow = row;
                    if (mainTable.getRowSorter() != null) {
                        modelRow = mainTable.convertRowIndexToModel(row);
                    }

                    excelTable.setRowSelected(modelRow, !currentValue);
                    if (currentValue) {
                        //取消选中
                        programmableSelectAll(false);
                    } else {
                        if (excelTable.isAllSelected()) {
                            programmableSelectAll(true);
                        }
                    }
                }
            }

            @Override
            public void mousePressed(MouseEvent e) {
                super.mousePressed(e);
                mouseRightButtonClick(e);
//                oldY = e.getY();
//                row = rowAtPoint(e.getPoint());
//                oldHeight = getRowHeight(row);
            }
        });
    }

    private void mouseRightButtonClick(MouseEvent e) {
        if (SwingUtilities.isRightMouseButton(e)) {
            //通过点击位置找到点击为表格中的行
            int focusedRowIndex = rowHeaderTable.rowAtPoint(e.getPoint());
            if (focusedRowIndex == -1) {
                return;
            }

            // 获取对应的模型行索引
            int modelRow = focusedRowIndex;
            if (mainTable.getRowSorter() != null) {
                modelRow = mainTable.convertRowIndexToModel(focusedRowIndex);
            }

            //将表格所选项设为当前右键点击的行
            rowHeaderTable.setRowSelectionInterval(focusedRowIndex, focusedRowIndex);
            // 主表需要选中过滤后的行索引
            mainTable.setRowSelectionInterval(focusedRowIndex, focusedRowIndex);
            if (mainTable.getComponentPopupMenu() != null) {
                mainTable.getComponentPopupMenu().show(rowHeaderTable, e.getX(), e.getY());
            }
        }
    }

    private void afterDataLoaded() {
        if (excelTable.isAllSelected()) {
            programmableSelectAll(true);
        }
    }

    public void programmableSelectAll(boolean selected) {
        EventQueue.invokeLater(() -> {
            enableSelectBoxChanged = false;
            selectCheckBox.setSelected(selected);
            head.repaint(); //重绘表头
            enableSelectBoxChanged = true;
        });
    }

    //全选
    public void selectAllOrNull(boolean value) {
        int rowCount = rowHeaderTable.getRowCount();

        // 对于大数据量使用批量优化
        if (rowCount > 1000) {
            batchSelectAllInFrozenTable(value);
        } else {
            for (int i = 0; i < rowCount; i++) {
                rowHeaderTable.getModel().setValueAt(value, i, 1);

                // 获取对应的模型行索引并更新ExcelTable的选中状态
                int modelRow = i;
                if (mainTable.getRowSorter() != null) {
                    try {
                        modelRow = mainTable.convertRowIndexToModel(i);
                    } catch (IndexOutOfBoundsException e) {
                        log.debug("转换视图行索引{}到模型行索引失败，跳过", i);
                        continue;
                    }
                }

                // 直接设置内部数据状态，避免再次调用setValueAt
                if (excelTable != null && modelRow < excelTable.getTableList().size()) {
                    excelTable.getTableList().get(modelRow).setSelected(value);
                }
            }
            // 同步更新主表的选中状态
            rowHeaderTable.repaint();
        }
    }

    /**
     * 批量全选FrozenColumnTable的复选框 - 极简高效版本，避免行高变化
     */
    private void batchSelectAllInFrozenTable(boolean value) {
        // 临时禁用同步，避免触发连锁反应
        boolean wasSyncing = isSyncing;
        isSyncing = true;

        try {
            TableModel headerModel = rowHeaderTable.getModel();
            int rowCount = rowHeaderTable.getRowCount();

            // 直接批量更新，不触发中间事件
            for (int i = 0; i < rowCount; i++) {
                headerModel.setValueAt(value, i, 1);

                // 获取对应的模型行索引并更新ExcelTable的选中状态
                int modelRow = i;
                if (mainTable.getRowSorter() != null) {
                    try {
                        modelRow = mainTable.convertRowIndexToModel(i);
                    } catch (IndexOutOfBoundsException e) {
                        log.debug("批量选择时转换视图行索引{}到模型行索引失败，跳过", i);
                        continue;
                    }
                }

                // 直接设置内部数据状态，避免再次调用setValueAt
                if (excelTable != null && modelRow < excelTable.getTableList().size()) {
                    excelTable.getTableList().get(modelRow).setSelected(value);
                }
            }

            // 只重绘，避免fireTableDataChanged触发行高重计算
            rowHeaderTable.repaint();

        } finally {
            isSyncing = wasSyncing;
        }
    }

    public void setFrozenColor(Integer row, Color color) {
        TableCellRenderer headRender = rowHeaderTable.getColumnModel().getColumn(0).getCellRenderer();
        ((RowHeaderRenderer) headRender).setColorForCell(row, 0, color);
    }

    public void clearFrozenColor(int row) {
        TableCellRenderer headRender = rowHeaderTable.getColumnModel().getColumn(0).getCellRenderer();
        ((RowHeaderRenderer) headRender).clearColor(row);
    }

    public void clearFrozenColor() {
        TableCellRenderer headRender = rowHeaderTable.getColumnModel().getColumn(0).getCellRenderer();
        ((RowHeaderRenderer) headRender).clearColor();
    }

    private void syncTableProperties() {
        fixedTable.setDefaultRenderer(Object.class, mainTable.getDefaultRenderer(Object.class));
        fixedTable.setDefaultEditor(Object.class, mainTable.getDefaultEditor(Object.class));
        fixedTable.setRowHeight(mainTable.getRowHeight());
        fixedTable.setRowMargin(mainTable.getRowMargin());
        fixedTable.setIntercellSpacing(mainTable.getIntercellSpacing());
        fixedTable.setGridColor(mainTable.getGridColor());
        fixedTable.setAutoResizeMode(mainTable.getAutoResizeMode());

        // 同步其他必要的属性
        fixedTable.setSelectionBackground(mainTable.getSelectionBackground());
        fixedTable.setSelectionForeground(mainTable.getSelectionForeground());
        fixedTable.setBackground(mainTable.getBackground());
        fixedTable.setForeground(mainTable.getForeground());
        fixedTable.setFont(mainTable.getFont());
    }

    public void updateFrozenColumns(int newFixedColumns) {
        // 创建新列模型
        TableColumnModel mainColumnModel = new DefaultTableColumnModel();
        TableColumnModel fixedColumnModel = new DefaultTableColumnModel();
        if (newFixedColumns != 0) {
            mainColumnModel.addColumn(allColumns.get(0));
        }
        // 添加固定列
        for (int i = 1; i < newFixedColumns; i++) {
            TableColumn column = allColumns.get(i);
            column.setHeaderValue("🔒 " + column.getHeaderValue().toString().replace("🔒 ", ""));
            fixedColumnModel.addColumn(column);
        }

        // 添加剩余列到主表
        for (int i = newFixedColumns; i < allColumns.size(); i++) {
            TableColumn column = allColumns.get(i);
            column.setHeaderValue(column.getHeaderValue().toString().replace("🔒 ", ""));
            mainColumnModel.addColumn(column);
        }

        mainTable.setColumnModel(mainColumnModel);
        fixedTable.setColumnModel(fixedColumnModel);

        // 为固定列和主表的表头添加右键菜单
        addHeaderPopupMenu(fixedTable.getTableHeader());
        addHeaderPopupMenu(mainTable.getTableHeader());

        // 同步行高
        syncRowHeights();

        scrollPane.setRowHeaderView(rowHeaderTable);
        scrollPane.setCorner(JScrollPane.UPPER_LEFT_CORNER, newFixedColumns > 0 ? fixedTable.getTableHeader() : null);
        scrollPane.setCorner(JScrollPane.UPPER_LEFT_CORNER, head);
    }

    private void syncRowHeights() {
        // **关键修改**：确保在EDT线程中执行
        if (!SwingUtilities.isEventDispatchThread()) {
            log.debug("syncRowHeights调度到EDT线程");
            SwingUtilities.invokeLater(this::syncRowHeights);
            return;
        }

        try {
            // 同步过滤后的可见行高
            for (int viewRow = 0; viewRow < mainTable.getRowCount(); viewRow++) {
                int modelRow = viewRow;
                if (mainTable.getRowSorter() != null) {
                    try {
                        modelRow = mainTable.convertRowIndexToModel(viewRow);
                    } catch (IndexOutOfBoundsException e) {
                        log.debug("转换视图行索引{}到模型行索引失败，跳过同步", viewRow);
                        continue;
                    }
                }

                int rowHeight = mainTable.getRowHeight(viewRow);
                if (viewRow < fixedTable.getRowCount()) {
                    fixedTable.setRowHeight(viewRow, rowHeight);
                }
                if (viewRow < rowHeaderTable.getRowCount()) {
                    rowHeaderTable.setRowHeight(viewRow, rowHeight);
                }
            }
        } catch (Exception e) {
            log.error("同步行高失败: {}", e.getMessage(), e);
        }
    }

    private void addHeaderPopupMenu(JTableHeader header) {
        header.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showHeaderPopupMenu(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showHeaderPopupMenu(e);
                }
            }
        });
    }

    private void showHeaderPopupMenu(MouseEvent e) {
        JTableHeader header = (JTableHeader) e.getSource();
        int columnIndex = header.columnAtPoint(e.getPoint());

        JPopupMenu popupMenu = new JPopupMenu();
        //筛选
        JMenuItem filterItem = new JMenuItem("筛选此列");
        JMenuItem cancelAllFilterItem = new JMenuItem("取消全部筛选");
        JMenuItem freezeItem = new JMenuItem("冻结到此列");
        JMenuItem unfreezeItem = new JMenuItem("取消冻结");

        filterItem.addActionListener(event -> {
            showColumnFilterDialog(columnIndex);

        });

        cancelAllFilterItem.addActionListener(event -> {
            showCancelAllFilterDialog();
        });

        freezeItem.addActionListener(event -> {
            // 计算新的固定列数
            int newFixedColumns = fixedColumns == 0 ? columnIndex + 1 : fixedColumns + columnIndex;
            toggleFreezeColumns(newFixedColumns);
        });
        unfreezeItem.addActionListener(event -> unfreezeColumns());

        popupMenu.add(filterItem);
        popupMenu.add(cancelAllFilterItem);
        popupMenu.add(freezeItem);
        popupMenu.add(unfreezeItem);

        popupMenu.show(header, e.getX(), e.getY());
    }

    private void showColumnFilterDialog(int columnIndex) {
        String columnName = excelTable.getModel().getColumnName(columnIndex);
//        log.info("columnName==" + columnName);
        Map<String, Map<String, Boolean>> tableHeaderDropDownOptions;
        Map<String, Boolean> options;
        CaseContent caseHeaderBySheetName = CaseConfigJsonManager.getCaseHeaderBySheetName(excelTable.getSheetName());
        if (caseHeaderBySheetName == null) {
            return;
        }
        tableHeaderDropDownOptions = caseHeaderBySheetName.getTableHeaderDropDownOptions();
        if (tableHeaderDropDownOptions == null) {
            JOptionPane.showMessageDialog(null, "该列头没有筛选下拉格式，无法进行筛选！");
            return;
        }
        if (tableHeaderDropDownOptions.containsKey(columnName)) {
            options = tableHeaderDropDownOptions.get(columnName);
            if (options != null && !options.isEmpty()) {
                // 创建一个弹窗，复选框显示选项，增加确定按钮，返回选择的选项
                String title = String.format("筛选列： %s", columnName);
                JDialog dialog = new JDialog();
                JPanel panel = new JPanel(new GridLayout(10, 5));
                // 创建一个滚动面板，将panel放入滚动面板中
                JScrollPane scrollPane = new JScrollPane(panel);
                scrollPane.setPreferredSize(new Dimension(300, 500)); // 固定大小
                scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS); // 始终显示垂直滚动条
                scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER); // 不显示水平滚动条
                JCheckBox allSelectedCheckBox = new JCheckBox("全选");
                panel.add(allSelectedCheckBox);
                JButton okButton = new JButton("确定");
                AtomicBoolean allSelected = new AtomicBoolean(true);
                options.forEach((optionName, selected) -> {
                    JCheckBox checkBox = new JCheckBox(optionName);
                    checkBox.setSelected(selected);
                    panel.add(checkBox);
                    if (!selected) {
                        allSelected.set(false);
                    }
                    allSelectedCheckBox.setSelected(allSelected.get());
                    checkBox.addItemListener(e1 -> {
                        if (!checkBox.isSelected()) {
                            allSelectedCheckBox.setSelected(false);
                        }
                    });
                });
                //动态监听，如果有没选中的选项，则allSelectedCheckBox应该被取消选中，如果全部选中，就要全选.子项的选中也影响全选的被选中
                allSelectedCheckBox.addItemListener(e1 -> {
                    if (allSelectedCheckBox.isSelected()) {
                        for (Component component : panel.getComponents()) {
                            JCheckBox checkBox = (JCheckBox) component;
                            checkBox.setSelected(true);
                        }
                    }
                });
                allSelectedCheckBox.setSelected(allSelected.get());
                okButton.addActionListener(e1 -> {
                    List<String> selectedOptions = new ArrayList<>();
                    for (Component component : panel.getComponents()) {
                        JCheckBox checkBox = (JCheckBox) component;
                        String text = checkBox.getText();
                        if (!text.equals("全选")) {
                            if (checkBox.isSelected()) {
                                selectedOptions.add(text);
                            }
                            //从options中获取option的key=checkBox.getText()的value
                            //把选中和没选中的记忆到options
                            options.put(text, checkBox.isSelected());
                        }
                    }
                    CaseConfigJsonManager.getCaseHeaderBySheetName(excelTable.getSheetName()).getTableHeaderDropDownOptions().put(columnName, options);
                    mainModel.getTestCaseTableModel().outputFilterTableHeaderInfo(true);
                    System.out.println("Selected options: " + Arrays.toString(selectedOptions.toArray()));
                    dialog.dispose();
                    List<CaseRowContent> caseRowContentList = CaseConfigJsonManager.getCaseHeaderBySheetName(excelTable.getSheetName()).getCaseRowContentList();
                    for (int i = 0; i < excelTable.getModel().getRowCount(); i++) {
                        Object value = excelTable.getModel().getValueAt(i, columnIndex);
                        String valueString = value == null ? "空白" : value.toString();
                        if (!selectedOptions.contains(valueString)) {
                            excelTable.addHiddenRow(i);
                            caseRowContentList.get(i).setHidden(true);
                        } else {
                            excelTable.removeHiddenRow(i);
                            caseRowContentList.get(i).setHidden(false);
                        }
                    }
                    excelTable.applyFiler();
                    mainModel.getTestCaseTableModel().outputFilterTableHeaderInfo(true);
                });

                dialog.setTitle(title);
                dialog.add(scrollPane);
                dialog.add(okButton, BorderLayout.SOUTH);
                dialog.setModal(true);
                dialog.pack();
                dialog.setLocationRelativeTo(null);
                dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
                dialog.setVisible(true);
                SwingUtil.centerInScreen(dialog);
            }
        }
    }

    private void showCancelAllFilterDialog() {
        //弹出提示窗口，有确定和取消按钮，点击确定，则取消所有筛选，点击取消，则不取消所有筛选
        int result = JOptionPane.showConfirmDialog(
                null,
                "确定取消表格的所有筛选吗？",
                "取消表格所有筛选",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE
        );

        if (result == JOptionPane.YES_OPTION) {
            Map<String, Map<String, Boolean>> tableHeaderDropDownOptions = CaseConfigJsonManager.getCaseHeaderBySheetName(
                    excelTable.getSheetName()).getTableHeaderDropDownOptions();
            tableHeaderDropDownOptions.forEach((columnName, options) -> {
                options.forEach((optionName, selected) -> {
                    options.put(optionName, true);
                });
            });
            List<CaseRowContent> caseRowContentList = CaseConfigJsonManager.getCaseHeaderBySheetName(excelTable.getSheetName()).getCaseRowContentList();
            for (int i = 0; i < excelTable.getModel().getRowCount(); i++) {
                caseRowContentList.get(i).setHidden(false);
            }
            excelTable.clearAllHiddenRows();
            mainModel.getTestCaseTableModel().outputFilterTableHeaderInfo(false);
        }
    }

    public void toggleFreezeColumns(int numColumns) {
        // 检查 numColumns 是否在有效范围内
        if (numColumns < 0 || numColumns > allColumns.size()) {
            return; // 如果 numColumns 不在有效范围内，直接返回
        }

        // 先取消冻结所有列
        unfreezeColumns();
        // 然后根据新列数重新冻结列
        if (numColumns > 0) {
            freezeColumns(numColumns);
        }
    }

    public void freezeColumns(int numColumns) {
        if (numColumns != fixedColumns && numColumns > 0 && numColumns <= allColumns.size()) {
            fixedColumns = numColumns;
            updateFrozenColumns(fixedColumns);
        }
    }

    public void unfreezeColumns() {
        if (fixedColumns > 0) {
            fixedColumns = 0;
            updateFrozenColumns(fixedColumns);
        }
    }

    private void syncVerticalScrolling() {
        // 确保 rowHeaderTable 随着主表的垂直滚动
        scrollPane.getVerticalScrollBar().addAdjustmentListener(e -> {
            // 避免在调整过程中频繁触发，只有在滚动结束时才同步
            if (!e.getValueIsAdjusting()) {
                JViewport rowViewport = scrollPane.getRowHeader();
                JViewport mainViewport = scrollPane.getViewport();
                if (rowViewport != null && mainViewport != null) {
                    Point mainViewPosition = mainViewport.getViewPosition();
                    Point rowViewPosition = rowViewport.getViewPosition();

                    // 只同步 Y 坐标，保持 X 坐标不变
                    // 增加阈值，避免微小差异引起的循环，并对大数据量做特殊处理
                    int threshold = mainTable.getRowCount() > 10000 ? 5 : 1;
                    if (Math.abs(rowViewPosition.y - mainViewPosition.y) > threshold) {
                        rowViewport.setViewPosition(new Point(rowViewPosition.x, mainViewPosition.y));
                    }
                }
            }
        });
    }

    /**
     * 在过滤器应用后调用，同步更新 rowHeaderTable
     */
    public void syncAfterFilter() {
        if (isSyncing) {
            return;
        }

        SwingUtilities.invokeLater(() -> {
            if (!isSyncing) {
                isSyncing = true;
                try {
                    // 通知 rowHeaderTable 数据已更新
                    ((AbstractTableModel) rowHeaderTable.getModel()).fireTableDataChanged();
                    // 同步行高
                    syncRowHeights();
                    // 刷新显示
                    rowHeaderTable.repaint();
                    scrollPane.repaint();
                } catch (Exception e) {
                    log.debug("syncAfterFilter时发生异常: {}", e.getMessage());
                } finally {
                    isSyncing = false;
                }
            }
        });
    }

    /**
     * 获取当前过滤后的可见行数
     */
    public int getVisibleRowCount() {
        return rowHeaderTable.getRowCount();
    }

    /**
     * 获取指定视图行对应的模型行索引
     */
    public int convertRowIndexToModel(int viewRowIndex) {
        if (mainTable.getRowSorter() != null && viewRowIndex >= 0 && viewRowIndex < mainTable.getRowCount()) {
            return mainTable.convertRowIndexToModel(viewRowIndex);
        }
        return viewRowIndex;
    }

    /**
     * 获取指定模型行对应的视图行索引
     */
    public int convertRowIndexToView(int modelRowIndex) {
        if (mainTable.getRowSorter() != null) {
            return mainTable.convertRowIndexToView(modelRowIndex);
        }
        return modelRowIndex;
    }

    public JComponent getComponent() {
        return scrollPane;
    }
}
